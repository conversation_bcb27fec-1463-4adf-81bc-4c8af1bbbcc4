import { createRouter, createWebHistory } from 'vue-router'
import MasterPage from '@/MasterPage.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'root',
      redirect: { name: 'lineage' },
      component: MasterPage,
      children: [
        {
          path: '/home',
          name: 'home',
          component: () => import('@/views/Home/MainPage.vue'),
          children: [
            {
              path: 'lineage',
              name: 'lineage',
              component: () => import('@/views/Home/lineage/MainPage.vue'),
            },
          ],
        },
      ],
    },
  ],
})

export default router
