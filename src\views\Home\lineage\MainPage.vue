<script setup>
import { ref } from 'vue'
import { DataSearchIcon } from 'tdesign-icons-vue-next'
import CommonBreadcrumb from '@/components/CommonBreadcrumb.vue'

const actived = ref('1')
const tabPanelList = [
  { value: '1', label: () => (<DataSearchIcon /> '血缘关系1') },
  { value: '2', label: '血缘关系2' },
]
</script>

<template>
  <div class="trace-lineage-page">
    <CommonBreadcrumb :breadcrumbs="['数据血缘']"></CommonBreadcrumb>
    <div class="trace-lineage-page__content">
      <t-tabs v-model="actived">
        <t-tab-panel
          v-for="item in tabPanelList"
          :value="item.value"
          :key="item.value"
          :label="item.label"
          lazy
        >
        </t-tab-panel>
      </t-tabs>
    </div>
  </div>
</template>

<style lang="less" scoped>
.trace-lineage-page {
  height: 100%;
  .trace-lineage-page__content {
    margin: var(--trace-lineage-margin);
    // 42px 为 breadcrumb 高度 + 上下margin
    height: calc(100% - 42px - var(--trace-lineage-margin) - var(--trace-lineage-margin));
    background-color: #fff;
  }
}
</style>
